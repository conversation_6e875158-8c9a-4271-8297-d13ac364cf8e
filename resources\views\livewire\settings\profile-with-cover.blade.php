<?php

use App\Models\UserCoverPhoto;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Volt\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

new class extends Component {
    public static $name = 'settings.profile-with-cover';
    use WithFileUploads;

    /**
     * The cover photo file upload.
     *
     * @var \Livewire\TemporaryUploadedFile|null
     */
    public $cover = null;

    /**
     * Whether to show the cropper interface.
     *
     * @var bool
     */
    public $showCropper = false;

    /**
     * The crop data from the cropper.
     *
     * @var array
     */
    public $cropData = [
        'x' => 0,
        'y' => 0,
        'width' => 0,
        'height' => 0
    ];

    /**
     * The temporary URL for the uploaded image.
     *
     * @var string|null
     */
    public $tempImageUrl = null;

    /**
     * Processing state for UI feedback.
     *
     * @var bool
     */
    public $processing = false;

    /**
     * Validation rules for the cover photo.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'cover' => ['required', 'image', 'mimes:jpg,jpeg,png', 'max:10240'], // 10MB max
        ];
    }

    /**
     * Check if crop data is valid.
     *
     * @return bool
     */
    private function hasCropData(): bool
    {
        return !empty($this->cropData['width']) && !empty($this->cropData['height']) &&
               $this->cropData['width'] > 10 && $this->cropData['height'] > 10;
    }

    /**
     * Force set initial crop data for testing.
     */
    public function setInitialCropData(): void
    {
        if (!$this->hasCropData() && $this->showCropper) {
            // Set default crop data if none exists
            $this->cropData = [
                'x' => 50,
                'y' => 50,
                'width' => 400,
                'height' => 300
            ];

            Log::info('Dados de recorte iniciais definidos', $this->cropData);
        }
    }

    /**
     * Update the user's cover photo.
     *
     * @return void
     */
    public function updateCover(): void
    {
        try {
            $this->processing = true;

            $user = Auth::user();

            if (!$user) {
                throw new \Exception('Authenticated user not found.');
            }

            $this->validate();

            if ($this->cover) {
                // Verificar se temos dados de recorte válidos, se não, definir padrões
                if (!$this->hasCropData()) {
                    Log::warning('Dados de recorte não encontrados, definindo valores padrão');
                    $this->setInitialCropData();

                    // Se ainda não temos dados válidos, usar a imagem inteira
                    if (!$this->hasCropData()) {
                        $this->cropData = [
                            'x' => 0,
                            'y' => 0,
                            'width' => 800, // Valor padrão razoável
                            'height' => 600 // Valor padrão razoável
                        ];
                        Log::info('Usando dados de recorte padrão para imagem inteira', $this->cropData);
                    }
                }

                // Log para debug
                Log::info('Iniciando processo de atualização da capa', [
                    'user_id' => $user->id,
                    'cropData' => $this->cropData
                ]);

                // Store the file and get the path
                $coverPath = $this->cover->store('covers', 'public');

                if (!$coverPath) {
                    throw new \Exception('Failed to store the cover photo.');
                }

                Log::info('Arquivo de capa salvo', [
                    'coverPath' => $coverPath
                ]);

                // Process the cropped image
                $croppedPath = $this->processCroppedImage($coverPath);

                // Se o processamento falhar, usamos o caminho original
                if (!$croppedPath) {
                    $croppedPath = $coverPath;
                    Log::warning('Usando imagem original como fallback', [
                        'coverPath' => $coverPath
                    ]);
                }

                // Verificar se já existe uma capa para este usuário
                $existingCover = UserCoverPhoto::where('user_id', $user->id)->latest()->first();

                if ($existingCover) {
                    Log::info('Atualizando capa existente', [
                        'existingCoverId' => $existingCover->id
                    ]);

                    // Atualizar a capa existente
                    $existingCover->update([
                        'photo_path' => $coverPath,
                        'crop_x' => $this->cropData['x'],
                        'crop_y' => $this->cropData['y'],
                        'crop_width' => $this->cropData['width'],
                        'crop_height' => $this->cropData['height'],
                        'cropped_photo_path' => $croppedPath
                    ]);
                } else {
                    // Create the cover photo record with crop data
                    UserCoverPhoto::create([
                        'user_id' => $user->id,
                        'photo_path' => $coverPath,
                        'crop_x' => $this->cropData['x'],
                        'crop_y' => $this->cropData['y'],
                        'crop_width' => $this->cropData['width'],
                        'crop_height' => $this->cropData['height'],
                        'cropped_photo_path' => $croppedPath
                    ]);
                }

                Log::info('Capa atualizada com sucesso', [
                    'user_id' => $user->id,
                    'coverPath' => $coverPath,
                    'croppedPath' => $croppedPath
                ]);

                $this->reset(['cover', 'showCropper', 'tempImageUrl', 'cropData']);
                $this->dispatch('cover-updated');
                session()->flash('success', 'Foto de capa atualizada com sucesso!');
            }
        } catch (\Throwable $e) {
            Log::error('Error updating cover photo: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            session()->flash('error', 'Ocorreu um erro ao atualizar a capa. Tente novamente.');
        } finally {
            $this->processing = false;
        }
    }

    /**
     * Process the cropped image and save it.
     *
     * @param string $originalPath The path to the original image
     * @return string|null The path to the cropped image or null on failure
     */
    protected function processCroppedImage(string $originalPath): ?string
    {
        try {
            // Validate crop data
            if (empty($this->cropData['width']) || empty($this->cropData['height'])) {
                Log::warning('Invalid or empty crop data', [
                    'cropData' => $this->cropData
                ]);
                // Se não houver dados de recorte válidos, retornar o caminho original
                return $originalPath;
            }

            // Get file paths
            $originalImage = Storage::disk('public')->path($originalPath);
            $croppedPath = 'covers/cropped_' . basename($originalPath);
            $croppedFullPath = Storage::disk('public')->path($croppedPath);

            // Create directory if it doesn't exist
            $directory = dirname($croppedFullPath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // Log para debug
            Log::info('Processando imagem recortada', [
                'originalPath' => $originalPath,
                'croppedPath' => $croppedPath,
                'cropData' => $this->cropData
            ]);

            // Create image manager instance with GD driver
            $manager = new ImageManager(new Driver());

            // Load image
            $image = $manager->read($originalImage);

            // Ensure crop values are valid positive integers
            $cropX = max(0, (int)$this->cropData['x']);
            $cropY = max(0, (int)$this->cropData['y']);
            $cropWidth = max(10, (int)$this->cropData['width']);
            $cropHeight = max(10, (int)$this->cropData['height']);

            // Verificar se os valores de recorte estão dentro dos limites da imagem
            $imageWidth = $image->width();
            $imageHeight = $image->height();

            // Ajustar coordenadas se necessário
            if ($cropX >= $imageWidth) {
                $cropX = 0;
            }
            if ($cropY >= $imageHeight) {
                $cropY = 0;
            }
            if ($cropX + $cropWidth > $imageWidth) {
                $cropWidth = $imageWidth - $cropX;
            }
            if ($cropY + $cropHeight > $imageHeight) {
                $cropHeight = $imageHeight - $cropY;
            }

            // Garantir que as dimensões sejam válidas
            if ($cropWidth <= 0) {
                $cropWidth = min(400, $imageWidth);
                $cropX = max(0, ($imageWidth - $cropWidth) / 2);
            }
            if ($cropHeight <= 0) {
                $cropHeight = min(300, $imageHeight);
                $cropY = max(0, ($imageHeight - $cropHeight) / 2);
            }

            // Log para debug
            Log::info('Dimensões finais de recorte', [
                'x' => $cropX,
                'y' => $cropY,
                'width' => $cropWidth,
                'height' => $cropHeight,
                'imageWidth' => $imageWidth,
                'imageHeight' => $imageHeight
            ]);

            // Crop the image
            $image->crop(
                $cropWidth,
                $cropHeight,
                $cropX,
                $cropY
            );

            // Save the cropped image
            $image->save($croppedFullPath);

            Log::info('Imagem recortada salva com sucesso', [
                'path' => $croppedPath
            ]);

            return $croppedPath;
        } catch (\Throwable $e) {
            Log::error('Error processing cropped image: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            // Em caso de erro, retornar o caminho original
            return $originalPath;
        }
    }

    /**
     * Set crop data from JavaScript.
     *
     * @param array $data The crop data
     * @return void
     */
    public function setCropData(array $data): void
    {
        try {
            // Verificar se os dados são válidos
            if (isset($data['x']) && isset($data['y']) && isset($data['width']) && isset($data['height'])) {
                // Garantir que todos os valores são números válidos
                $x = is_numeric($data['x']) ? max(0, (int)$data['x']) : 0;
                $y = is_numeric($data['y']) ? max(0, (int)$data['y']) : 0;
                $width = is_numeric($data['width']) ? max(10, (int)$data['width']) : 10;
                $height = is_numeric($data['height']) ? max(10, (int)$data['height']) : 10;

                $this->cropData = [
                    'x' => $x,
                    'y' => $y,
                    'width' => $width,
                    'height' => $height
                ];

                // Log para debug apenas se os valores mudaram significativamente
                if (abs($this->cropData['x'] - $x) > 5 || abs($this->cropData['y'] - $y) > 5 ||
                    abs($this->cropData['width'] - $width) > 5 || abs($this->cropData['height'] - $height) > 5) {
                    Log::info('Dados de recorte atualizados', $this->cropData);
                }
            } else {
                Log::warning('Dados de recorte inválidos recebidos', [
                    'data' => $data,
                    'missing_keys' => array_diff(['x', 'y', 'width', 'height'], array_keys($data))
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao definir dados de recorte: ' . $e->getMessage(), [
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle file upload and prepare for cropping.
     *
     * @return void
     */
    public function updatedCover(): void
    {
        if ($this->cover) {
            try {
                // Validar o arquivo antes de processar
                $this->validate([
                    'cover' => ['required', 'image', 'mimes:jpg,jpeg,png', 'max:10240']
                ]);

                $this->tempImageUrl = $this->cover->temporaryUrl();
                $this->showCropper = true;

                // Reset crop data when new image is uploaded
                $this->cropData = [
                    'x' => 0,
                    'y' => 0,
                    'width' => 0,
                    'height' => 0
                ];

                $this->dispatch('cover-image-uploaded');

                Log::info('Imagem de capa carregada para recorte', [
                    'tempUrl' => $this->tempImageUrl
                ]);
            } catch (\Exception $e) {
                Log::error('Erro ao processar upload da capa: ' . $e->getMessage());
                $this->reset(['cover', 'showCropper', 'tempImageUrl']);
                session()->flash('error', 'Erro ao carregar a imagem. Verifique o formato e tamanho do arquivo.');
            }
        }
    }

    /**
     * Cancel cropping.
     *
     * @return void
     */
    public function cancelCrop(): void
    {
        $this->reset(['cover', 'showCropper', 'tempImageUrl', 'cropData']);
    }
}; ?>

<section class="w-full profile-with-cover">
    <!-- Estilos inline para garantir que o Cropper.js funcione corretamente -->
    <style>
        /* Container principal do cropper */
        #crop-container {
            position: relative;
            width: 100%;
            height: 500px;
            background-color: #f3f4f6;
            border-radius: 0.5rem;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Imagem original - configuração inicial */
        #coverImage {
            display: block;
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        /* Quando o cropper estiver ativo, ajustar a imagem */
        #crop-container.cropper-active #coverImage {
            display: block;
            max-width: none;
            max-height: none;
            width: auto;
            height: auto;
        }

        /* Container do cropper */
        .profile-with-cover .cropper-container {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            direction: ltr !important;
            touch-action: none !important;
            user-select: none !important;
            background-color: transparent !important;
        }

        /* Canvas do cropper */
        .profile-with-cover .cropper-canvas {
            position: absolute !important;
        }

        /* Imagem dentro do cropper */
        .profile-with-cover .cropper-canvas > img {
            width: auto !important;
            height: auto !important;
            max-width: none !important;
            max-height: none !important;
        }

        /* Área de visualização */
        .profile-with-cover .cropper-view-box {
            display: block !important;
            overflow: hidden !important;
            width: 100% !important;
            height: 100% !important;
            outline: 3px solid #3b82f6 !important;
            outline-offset: -2px !important;
            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
        }

        /* Área de recorte */
        .profile-with-cover .cropper-crop-box {
            position: absolute !important;
        }

        /* Face da área de recorte */
        .profile-with-cover .cropper-face {
            background-color: transparent !important;
            opacity: 0.1 !important;
        }

        /* Melhorar visibilidade da área selecionada */
        .profile-with-cover .cropper-view-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px dashed rgba(59, 130, 246, 0.8);
            pointer-events: none;
        }

        /* Linhas de guia */
        .profile-with-cover .cropper-line {
            background-color: #3b82f6 !important;
            opacity: 0.6 !important;
        }

        /* Pontos de controle */
        .profile-with-cover .cropper-point {
            width: 8px !important;
            height: 8px !important;
            background-color: #3b82f6 !important;
            opacity: 0.8 !important;
            border-radius: 50% !important;
        }

        /* Modal/overlay */
        .profile-with-cover .cropper-modal {
            background-color: rgba(0, 0, 0, 0.5) !important;
        }

        /* Modo escuro */
        .dark #crop-container {
            background-color: #1f2937;
        }

        .dark .profile-with-cover .cropper-view-box {
            outline-color: #60a5fa !important;
        }

        .dark .profile-with-cover .cropper-line {
            background-color: #60a5fa !important;
        }

        .dark .profile-with-cover .cropper-point {
            background-color: #60a5fa !important;
        }

        /* Responsivo */
        @media (max-width: 640px) {
            #crop-container {
                height: 300px;
            }
        }
    </style>

    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Cover')" :subheading="__('Update your cover photo')">
        <form wire:submit.prevent="updateCover" class="my-6 w-full space-y-6">
            <div>
                <!-- File upload component -->
                <x-file-upload
                    wire:model="cover"
                    label="Foto de Capa"
                    accept="image/png, image/jpeg"
                    icon="photo"
                    :iconVariant="$cover ? 'solid' : 'outline'"
                    help="Selecione uma imagem para usar como capa do seu perfil. Tamanho máximo: 10MB."
                />

                @error('cover')
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror

                <!-- Cropper interface -->
                @if($showCropper && $tempImageUrl)
                    <div class="mt-4">
                        <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            Selecione a área da imagem que deseja usar como capa (arraste e redimensione livremente)
                        </div>

                        <!-- Instruções de uso -->
                        <div class="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                <strong>Como usar:</strong> Arraste para mover a área de recorte, use as bordas para redimensionar.
                                A área destacada em azul será a parte da imagem que será salva como sua capa.
                            </p>
                        </div>

                        <!-- Cropper container -->
                        <div wire:ignore id="crop-container" class="mb-4">
                            <img
                                id="coverImage"
                                src="{{ $tempImageUrl }}"
                                alt="Imagem para recorte"
                                crossorigin="anonymous"
                            >
                        </div>

                        <!-- Debug info -->
                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                            Dados de recorte: X={{ $cropData['x'] ?? 0 }}, Y={{ $cropData['y'] ?? 0 }},
                            Largura={{ $cropData['width'] ?? 0 }}, Altura={{ $cropData['height'] ?? 0 }}
                        </div>

                        <!-- Debug controls (apenas em desenvolvimento) -->
                        @if(config('app.debug'))
                        <div class="flex items-center gap-2 mb-2">
                            <button
                                type="button"
                                onclick="debugCropper()"
                                class="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded"
                            >
                                Debug Info
                            </button>
                            <button
                                type="button"
                                onclick="forceCropperCleanup()"
                                class="px-2 py-1 text-xs bg-red-200 dark:bg-red-700 text-red-700 dark:text-red-300 rounded"
                            >
                                Limpeza Forçada
                            </button>
                        </div>
                        @endif

                        <!-- Crop controls -->
                        <div class="flex items-center gap-2 mt-4">
                            <x-flux::button
                                type="button"
                                variant="ghost"
                                wire:click="setInitialCropData"
                                class="flex-1"
                                :disabled="$processing"
                            >
                                Definir Área Padrão
                            </x-flux::button>

                            <x-flux::button
                                type="button"
                                variant="primary"
                                wire:click="cancelCrop"
                                class="flex-1"
                                :disabled="$processing"
                            >
                                Cancelar
                            </x-flux::button>

                            <x-flux::button
                                type="submit"
                                variant="primary"
                                class="flex-1"
                                :disabled="$processing"
                                wire:loading.attr="disabled"
                                wire:target="updateCover"
                            >
                                <span wire:loading.remove wire:target="updateCover">
                                    Salvar
                                </span>
                                <span wire:loading wire:target="updateCover">
                                    Salvando...
                                </span>
                            </x-flux::button>
                        </div>
                    </div>
                @elseif (auth()->user() && auth()->user()->userCoverPhotos()->latest()->first())
                    @php
                        $coverPhoto = auth()->user()->userCoverPhotos()->latest()->first();
                        $photoPath = $coverPhoto->cropped_photo_path ?? $coverPhoto->photo_path;
                    @endphp

                    <div class="mt-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            Foto de capa atual
                        </p>
                        <img
                            src="{{ Storage::url($photoPath) }}"
                            alt="Capa"
                            class="w-full h-40 rounded-lg object-cover border border-gray-200 dark:border-gray-700"
                        >
                    </div>
                @endif
            </div>

            <!-- Action buttons and messages -->
            <div class="flex items-center gap-4">
                @if(!$showCropper)
                    <div class="flex items-center justify-end">
                        <x-flux::button
                            variant="primary"
                            type="submit"
                            class="w-full"
                            :disabled="!$cover || $processing"
                            wire:loading.attr="disabled"
                            wire:target="updateCover"
                        >
                            <span wire:loading.remove wire:target="updateCover">
                                Salvar
                            </span>
                            <span wire:loading wire:target="updateCover">
                                Salvando...
                            </span>
                        </x-flux::button>
                    </div>
                @endif

                <x-action-message class="me-3" on="cover-updated">
                    Foto de capa atualizada com sucesso.
                </x-action-message>

                @if (session('success'))
                    <p class="mt-2 font-medium text-green-600 dark:text-green-400">
                        {{ session('success') }}
                    </p>
                @endif

                @if (session('error'))
                    <p class="mt-2 font-medium text-red-600 dark:text-red-400">
                        {{ session('error') }}
                    </p>
                @endif
            </div>
        </form>
    </x-settings.layout>

    <!-- Cropper.js initialization script -->
    <script>
        document.addEventListener('livewire:initialized', function() {
            console.log('[Cover Cropper] Livewire inicializado');

            let cropper = null;
            let initializationTimeout = null;
            let cropDataSent = false;

            // Função para verificar se o Cropper.js está disponível
            function isCropperAvailable() {
                const available = typeof window.Cropper !== 'undefined';
                console.log('[Cover Cropper] Cropper.js disponível:', available);
                return available;
            }

            // Função para aguardar o Cropper.js estar disponível
            function waitForCropper(callback, maxAttempts = 50) {
                let attempts = 0;
                console.log('[Cover Cropper] Aguardando Cropper.js...');

                function check() {
                    attempts++;
                    if (isCropperAvailable()) {
                        console.log('[Cover Cropper] Cropper.js encontrado após', attempts, 'tentativas');
                        callback();
                    } else if (attempts < maxAttempts) {
                        setTimeout(check, 100);
                    } else {
                        console.error('[Cover Cropper] Cropper.js não foi carregado após', maxAttempts, 'tentativas');
                        // Tentar carregar via CDN como fallback
                        loadCropperFallback(callback);
                    }
                }

                check();
            }

            // Função para carregar Cropper.js via CDN como fallback
            function loadCropperFallback(callback) {
                console.log('[Cover Cropper] Tentando carregar Cropper.js via CDN...');

                // Carregar CSS
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/2.0.0/cropper.min.css';
                document.head.appendChild(link);

                // Carregar JS
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/2.0.0/cropper.min.js';
                script.onload = function() {
                    console.log('[Cover Cropper] Cropper.js carregado via CDN');
                    window.Cropper = Cropper;
                    callback();
                };
                script.onerror = function() {
                    console.error('[Cover Cropper] Falha ao carregar Cropper.js via CDN');
                };
                document.head.appendChild(script);
            }

            // Função para destruir o cropper existente
            function destroyCropper() {
                if (cropper) {
                    try {
                        // Verificar se é uma instância válida do Cropper
                        if (typeof cropper.destroy === 'function') {
                            cropper.destroy();
                            console.log('[Cover Cropper] Cropper destruído com sucesso');
                        } else {
                            console.warn('[Cover Cropper] Cropper não é uma instância válida:', typeof cropper);
                        }
                    } catch (e) {
                        console.warn('[Cover Cropper] Erro ao destruir cropper:', e);
                    }
                    cropper = null;
                }

                // Remover classe do container
                const container = document.getElementById('crop-container');
                if (container) {
                    container.classList.remove('cropper-active');

                    // Remover qualquer elemento do cropper que possa ter ficado
                    const cropperContainer = container.querySelector('.cropper-container');
                    if (cropperContainer) {
                        try {
                            cropperContainer.remove();
                            console.log('[Cover Cropper] Container do cropper removido manualmente');
                        } catch (e) {
                            console.warn('[Cover Cropper] Erro ao remover container do cropper:', e);
                        }
                    }
                }

                cropDataSent = false;
            }

            // Função para enviar dados de crop
            function sendCropData(data) {
                if (cropDataSent) return; // Evitar envios duplicados

                console.log('[Cover Cropper] Enviando dados de crop:', data);

                if (typeof $wire !== 'undefined' && $wire.setCropData) {
                    try {
                        $wire.setCropData(data);
                        cropDataSent = true;
                        console.log('[Cover Cropper] Dados de crop enviados com sucesso');

                        // Reset flag após um tempo
                        setTimeout(() => {
                            cropDataSent = false;
                        }, 1000);
                    } catch (error) {
                        console.error('[Cover Cropper] Erro ao enviar dados de crop:', error);
                        cropDataSent = false;
                    }
                } else {
                    console.warn('[Cover Cropper] $wire não disponível');
                }
            }

            // Função para inicializar o cropper
            function initCropper() {
                const image = document.getElementById('coverImage');

                if (!image) {
                    console.warn('[Cover Cropper] Elemento coverImage não encontrado');
                    return;
                }

                if (!isCropperAvailable()) {
                    console.warn('[Cover Cropper] Cropper.js não está disponível');
                    return;
                }

                // Verificar se a imagem foi carregada
                if (!image.complete || image.naturalWidth === 0) {
                    console.warn('[Cover Cropper] Imagem não foi carregada completamente');
                    return;
                }

                // Destruir instância anterior se existir
                destroyCropper();

                console.log('[Cover Cropper] Inicializando Cropper.js');
                console.log('[Cover Cropper] Dimensões da imagem:', image.naturalWidth, 'x', image.naturalHeight);

                try {
                    // Marcar container como ativo
                    const container = document.getElementById('crop-container');
                    if (container) {
                        container.classList.add('cropper-active');
                    }

                    // Verificar se o Cropper está disponível globalmente
                    const CropperClass = window.Cropper;
                    if (!CropperClass || typeof CropperClass !== 'function') {
                        console.error('[Cover Cropper] Classe Cropper não encontrada');
                        return;
                    }

                    // Inicializar nova instância
                    cropper = new CropperClass(image, {
                        viewMode: 1,
                        dragMode: 'crop',
                        aspectRatio: NaN, // Permitir qualquer proporção
                        autoCropArea: 0.7,
                        responsive: true,
                        restore: false,
                        guides: true,
                        center: true,
                        highlight: true,
                        cropBoxMovable: true,
                        cropBoxResizable: true,
                        toggleDragModeOnDblclick: false,
                        minCropBoxWidth: 100,
                        minCropBoxHeight: 75,
                        background: true,
                        modal: true,
                        scalable: true,
                        zoomable: true,
                        rotatable: false,
                        checkCrossOrigin: false,
                        checkOrientation: false,
                        ready: function() {
                            console.log('[Cover Cropper] Cropper pronto');

                            // Aguardar um pouco antes de definir a área inicial
                            setTimeout(() => {
                                try {
                                    const imageData = cropper.getImageData();
                                    const canvasData = cropper.getCanvasData();
                                    const containerData = cropper.getContainerData();

                                    console.log('[Cover Cropper] Image data:', imageData);
                                    console.log('[Cover Cropper] Canvas data:', canvasData);
                                    console.log('[Cover Cropper] Container data:', containerData);

                                    // Usar a área de crop padrão que o cropper já definiu
                                    const currentData = cropper.getData();

                                    // Se os dados atuais são válidos, usar eles
                                    if (currentData.width > 0 && currentData.height > 0) {
                                        console.log('[Cover Cropper] Usando área de recorte padrão do cropper:', currentData);

                                        sendCropData({
                                            x: Math.round(currentData.x),
                                            y: Math.round(currentData.y),
                                            width: Math.round(currentData.width),
                                            height: Math.round(currentData.height)
                                        });
                                    } else {
                                        // Fallback: definir área manual
                                        const imageWidth = imageData.naturalWidth;
                                        const imageHeight = imageData.naturalHeight;

                                        // Área mais conservadora - 60% da imagem
                                        const cropWidth = Math.min(imageWidth * 0.6, 800);
                                        const cropHeight = Math.min(imageHeight * 0.6, 600);
                                        const cropX = (imageWidth - cropWidth) / 2;
                                        const cropY = (imageHeight - cropHeight) / 2;

                                        console.log('[Cover Cropper] Definindo área de recorte manual:', {
                                            x: cropX,
                                            y: cropY,
                                            width: cropWidth,
                                            height: cropHeight
                                        });

                                        cropper.setData({
                                            x: cropX,
                                            y: cropY,
                                            width: cropWidth,
                                            height: cropHeight,
                                            rotate: 0,
                                            scaleX: 1,
                                            scaleY: 1
                                        });

                                        sendCropData({
                                            x: Math.round(cropX),
                                            y: Math.round(cropY),
                                            width: Math.round(cropWidth),
                                            height: Math.round(cropHeight)
                                        });
                                    }
                                } catch (error) {
                                    console.error('[Cover Cropper] Erro ao definir área inicial:', error);
                                }
                            }, 500);
                        },
                        crop: function(event) {
                            const data = {
                                x: Math.round(event.detail.x),
                                y: Math.round(event.detail.y),
                                width: Math.round(event.detail.width),
                                height: Math.round(event.detail.height)
                            };

                            console.log('[Cover Cropper] Evento crop:', data);

                            if (data.width > 10 && data.height > 10) {
                                sendCropData(data);
                            }
                        },
                        cropstart: function(event) {
                            console.log('[Cover Cropper] Início do crop');
                        },
                        cropend: function(event) {
                            console.log('[Cover Cropper] Fim do crop');
                        }
                    });

                    // Verificar se a instância foi criada corretamente
                    if (cropper && typeof cropper.destroy === 'function') {
                        console.log('[Cover Cropper] Cropper inicializado com sucesso');
                        console.log('[Cover Cropper] Tipo da instância:', typeof cropper);
                        console.log('[Cover Cropper] Métodos disponíveis:', Object.getOwnPropertyNames(Object.getPrototypeOf(cropper)));
                    } else {
                        console.error('[Cover Cropper] Instância do cropper inválida');
                        cropper = null;
                    }
                } catch (error) {
                    console.error('[Cover Cropper] Erro ao inicializar Cropper.js:', error);
                    cropper = null;

                    // Remover classe do container em caso de erro
                    const container = document.getElementById('crop-container');
                    if (container) {
                        container.classList.remove('cropper-active');
                    }
                }
            }

            // Inicializar quando a imagem for carregada
            Livewire.on('cover-image-uploaded', function() {
                console.log('[Cover Cropper] Evento cover-image-uploaded recebido');

                // Limpar timeout anterior se existir
                if (initializationTimeout) {
                    clearTimeout(initializationTimeout);
                }

                // Aguardar o DOM atualizar e o Cropper.js estar disponível
                initializationTimeout = setTimeout(function() {
                    console.log('[Cover Cropper] Iniciando processo de inicialização...');

                    waitForCropper(function() {
                        const image = document.getElementById('coverImage');

                        if (image) {
                            console.log('[Cover Cropper] Imagem encontrada:', image.src);
                            console.log('[Cover Cropper] Imagem carregada:', image.complete);
                            console.log('[Cover Cropper] Dimensões naturais:', image.naturalWidth, 'x', image.naturalHeight);

                            if (image.complete && image.naturalWidth > 0) {
                                initCropper();
                            } else {
                                console.log('[Cover Cropper] Aguardando carregamento da imagem...');
                                image.onload = function() {
                                    console.log('[Cover Cropper] Imagem carregada, inicializando cropper');
                                    initCropper();
                                };
                                image.onerror = function() {
                                    console.error('[Cover Cropper] Erro ao carregar a imagem');
                                };
                            }
                        } else {
                            console.warn('[Cover Cropper] Imagem não encontrada após timeout');
                        }
                    });
                }, 500);
            });

            // Adicionar função global para debug
            window.debugCropper = function() {
                console.log('[Cover Cropper] Debug info:');
                console.log('- Cropper instance:', cropper);
                console.log('- Cropper type:', typeof cropper);
                console.log('- Cropper available:', isCropperAvailable());
                console.log('- Image element:', document.getElementById('coverImage'));
                console.log('- Container element:', document.getElementById('crop-container'));

                if (cropper) {
                    console.log('- Has destroy method:', typeof cropper.destroy === 'function');

                    try {
                        console.log('- Crop data:', cropper.getData());
                        console.log('- Canvas data:', cropper.getCanvasData());
                        console.log('- Container data:', cropper.getContainerData());
                        console.log('- Image data:', cropper.getImageData());
                    } catch (e) {
                        console.error('- Error getting cropper data:', e);
                    }
                } else {
                    console.log('- Cropper instance is null or undefined');
                }

                // Verificar se há elementos do cropper no DOM
                const cropperContainers = document.querySelectorAll('.cropper-container');
                console.log('- Cropper containers in DOM:', cropperContainers.length);

                // Verificar window.Cropper
                console.log('- window.Cropper:', typeof window.Cropper);
                console.log('- window.Cropper constructor:', window.Cropper);
            };

            // Função para limpeza forçada do DOM
            window.forceCropperCleanup = function() {
                console.log('[Cover Cropper] Executando limpeza forçada...');

                // Destruir cropper se existir
                destroyCropper();

                // Remover todos os elementos do cropper do DOM
                const cropperContainers = document.querySelectorAll('.cropper-container');
                cropperContainers.forEach(container => {
                    try {
                        container.remove();
                        console.log('[Cover Cropper] Container removido:', container);
                    } catch (e) {
                        console.warn('[Cover Cropper] Erro ao remover container:', e);
                    }
                });

                // Resetar variáveis
                cropper = null;
                cropDataSent = false;

                // Remover classe do container principal
                const mainContainer = document.getElementById('crop-container');
                if (mainContainer) {
                    mainContainer.classList.remove('cropper-active');
                }

                console.log('[Cover Cropper] Limpeza forçada concluída');
            };

            // Limpar quando o componente for desconectado
            document.addEventListener('livewire:disconnected', function() {
                console.log('[Cover Cropper] Livewire desconectado, limpando recursos');
                destroyCropper();
                if (initializationTimeout) {
                    clearTimeout(initializationTimeout);
                }
            });

            // Limpar quando a página for descarregada
            window.addEventListener('beforeunload', function() {
                console.log('[Cover Cropper] Página sendo descarregada, limpando recursos');
                destroyCropper();
            });

            // Log inicial
            console.log('[Cover Cropper] Script inicializado');
            console.log('[Cover Cropper] Cropper.js disponível:', isCropperAvailable());
        });
    </script>
</section>